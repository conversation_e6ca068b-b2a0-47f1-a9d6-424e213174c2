# Application
MOCK_MODE=true
PYTHONPATH=/app

# Database / Cache
DATABASE_URL=postgresql+psycopg2://ecuser:ecpass@db:5432/ecsaas
REDIS_URL=redis://redis:6379/0

# OpenAI (LLM)
OPENAI_API_KEY=
# モデルは省略時 gpt-4o-mini を使用
OPENAI_MODEL=gpt-4o-mini

# Tavily (Web 検索)
TAVILY_API_KEY=

# OxyLabs（将来のスクレイピング想定）
OXYLABS_USER=
OXYLABS_PASS=

# Marketplaces
YAHOO_API_KEY=
AMAZON_ACCESS_KEY_ID=
AMAZON_SECRET_ACCESS_KEY=
RAKUTEN_APP_ID=

# Docker Compose 用 Postgres デフォルト（必要な場合のみ上書き）
POSTGRES_USER=ecuser
POSTGRES_PASSWORD=ecpass
POSTGRES_DB=ecsaas