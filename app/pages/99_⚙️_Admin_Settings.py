import streamlit as st
from app.data_access.repositories import upsert_provider_config, get_all_provider_configs

st.set_page_config(page_title="Admin Settings", page_icon="⚙️")

st.title("⚙️ Admin Settings")
with st.form("settings"):
    st.subheader("APIキー / 設定")
    openai = st.text_input("OPENAI_API_KEY", type="password")
    tavily = st.text_input("TAVILY_API_KEY", type="password")
    oxu = st.text_input("OXYLABS_USER")
    oxp = st.text_input("OXYLABS_PASS", type="password")
    yahoo = st.text_input("YAHOO_API_KEY", type="password")
    amz_ak = st.text_input("AMAZON_ACCESS_KEY_ID")
    amz_sk = st.text_input("AMAZON_SECRET_ACCESS_KEY", type="password")
    rakuten = st.text_input("RAKUTEN_APP_ID")
    mock_mode = st.checkbox("モックを優先 (MOCK_MODE)", value=True)
    submitted = st.form_submit_button("保存")

if submitted:
    upsert_provider_config({"name": "openai", "type": "llm", "api_key": openai})
    upsert_provider_config({"name": "tavily", "type": "search", "api_key": tavily})
    upsert_provider_config({"name": "oxylabs", "type": "scrape", "api_key": oxu, "secret": oxp})
    upsert_provider_config({"name": "yahoo", "type": "market", "api_key": yahoo})
    upsert_provider_config({"name": "amazon", "type": "market", "api_key": amz_ak, "secret": amz_sk})
    upsert_provider_config({"name": "rakuten", "type": "market", "api_key": rakuten})
    upsert_provider_config({"name": "app", "type": "app", "extra_json": {"MOCK_MODE": mock_mode}})
    st.success("保存しました")

st.subheader("現在の設定 (DB)")
st.json(get_all_provider_configs())