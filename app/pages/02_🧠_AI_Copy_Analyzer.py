import streamlit as st
from app.services.registry import get_llm

st.set_page_config(page_title="AI Copy Analyzer", page_icon="🧠")

st.title("🧠 AI Copy Analyzer")
text = st.text_area("分析するテキスト", "臨場感あるサウンド体験。長時間装着でも疲れないフィット感。")
run = st.button("分析")

if run and text:
    llm = get_llm()
    with st.spinner("分析中..."):
        result = llm.analyze_copy(text)
    st.success("完了")
    col1, col2 = st.columns(2)
    with col1:
        st.metric("読みやすさ", result.get("readability", 0))
        st.metric("感情スコア", result.get("sentiment", 0))
    with col2:
        st.markdown("**訴求ポイント**")
        for p in result.get("key_points", []):
            st.write("- ", p)
        st.markdown("**改善案**")
        for imp in result.get("improvements", []):
            st.write("- ", imp)