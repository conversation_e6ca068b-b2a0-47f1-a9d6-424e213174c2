import streamlit as st
from app.services.pricing.rules import apply_pricing
from app.services.registry import get_market_adapter

st.set_page_config(page_title="Dynamic Pricing", page_icon="💹")

st.title("💹 Dynamic Pricing")
sku = st.text_input("SKU", "SKU-001")
min_margin = st.slider("最低利益率(%)", 0, 50, 10)
follow_comp = st.checkbox("競合価格に追従", True)
stock = st.number_input("在庫", 0, 5000, 120)
season = st.selectbox("季節", ["spring", "summer", "autumn", "winter"]) 
run = st.button("計算")

if run and sku:
    adapter = get_market_adapter(prefer="amazon")
    competitors = adapter.fetch_competitors(sku)
    product = {"sku": sku, "base_price": 9800, "cost": 5000}
    result = apply_pricing(product, competitors, stock, season, {
        "min_margin_pct": min_margin / 100.0,
        "follow_competitor": follow_comp
    })
    st.success("計算完了")
    st.metric("推奨価格", f"¥{result['recommended_price']}")
    st.write("根拠:")
    for r in result.get("rationale", []):
        st.write("- ", r)
    st.write("シミュレーション:")
    st.table(result.get("simulation", {}))