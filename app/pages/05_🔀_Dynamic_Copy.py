import streamlit as st
from app.services.dynamic_copy.rules import select_copy_variant

st.set_page_config(page_title="Dynamic Copy", page_icon="🔀")

st.title("🔀 Dynamic Copy")
conditions = {
    "low_stock": st.checkbox("在庫少", False),
    "sale": st.checkbox("セール期間", True),
    "season": st.selectbox("季節", ["spring", "summer", "autumn", "winter"])
}
if st.button("プレビュー"):
    text = select_copy_variant(conditions, product={"title": "ワイヤレスイヤホン"})
    st.text_area("有効コピー", text, height=120)