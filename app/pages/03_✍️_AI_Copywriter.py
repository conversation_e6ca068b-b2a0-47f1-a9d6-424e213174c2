import streamlit as st
from app.services.registry import get_llm
from app.data_access.repositories import save_copy_variant, list_copy_variants

st.set_page_config(page_title="AI Copywriter", page_icon="✍️")

st.title("✍️ AI Copywriter")
product = st.text_input("商品名", "ワイヤレスイヤホン Pro")
features = st.text_area("特徴", "ノイズキャンセリング, 長時間バッテリー, 低遅延")
tone = st.selectbox("トーン", ["カジュアル", "プロフェッショナル", "情緒的"])
max_tokens = st.slider("最大文字数", 50, 400, 120)
run = st.button("生成")

if run and product:
    llm = get_llm()
    with st.spinner("生成中..."):
        copy = llm.generate_copy({"title": product, "features": features}, tone, max_tokens)
        save_copy_variant({
            "product_title": product,
            "variant_type": "ai",
            "text": copy,
            "source": "llm",
        })
    st.success("保存しました")
    st.text_area("候補", value=copy, height=200)

st.subheader("保存済み")
for row in list_copy_variants(limit=5):
    st.write("- ", row.get("text"))