import streamlit as st
import pandas as pd
from app.services.registry import get_market_adapter, get_search_adapter
from app.data_access.repositories import save_competitor_listings

st.set_page_config(page_title="Market Intelligence", page_icon="🏪")

st.title("🏪 Market Intelligence")
query = st.text_input("キーワード / ASIN / 商品URL", "ワイヤレスイヤホン")
run = st.button("競合を取得")

if run and query:
    adapter = get_market_adapter(prefer="amazon")
    search = get_search_adapter()
    with st.spinner("取得中..."):
        competitors = adapter.fetch_competitors(query)
        if not competitors:
            serp = search.search_products(query)
            competitors = serp
        df = pd.DataFrame(competitors)
        save_competitor_listings(df.to_dict(orient="records"))
    st.success("取得完了")
    st.dataframe(df, use_container_width=True)
    if "price" in df.columns:
        st.bar_chart(df["price"])