import os
from typing import Literal
from app.services.adapters.mock_provider import MockMarketAdapter
from app.services.adapters.amazon import AmazonAdapter
from app.services.adapters.yahoo import YahooAdapter
from app.services.adapters.rakuten import <PERSON><PERSON><PERSON>Adapter
from app.services.adapters.tavily import <PERSON><PERSON><PERSON>dapter
from app.services.llm.openai_provider import OpenAILLM
from app.data_access.repositories import get_provider_config


def _use_mock() -> bool:
    # env最優先
    env_mock = os.environ.get("MOCK_MODE")
    if env_mock is not None:
        return env_mock.lower() == "true"
    # DBに保存されたアプリ設定
    app_cfg = get_provider_config("app")
    if app_cfg and app_cfg.get("extra_json") and app_cfg["extra_json"].get("MOCK_MODE") is True:
        return True
    return False


def get_market_adapter(prefer: Literal["amazon", "yahoo", "rakuten"] = "amazon"):
    if _use_mock():
        return MockMarketAdapter()
    if prefer == "amazon":
        cfg = get_provider_config("amazon")
        if not cfg or not cfg.get("api_key") or not cfg.get("secret"):
            return MockMarketAdapter()
        return AmazonAdapter()
    if prefer == "yahoo":
        cfg = get_provider_config("yahoo")
        if not cfg or not cfg.get("api_key"):
            return MockMarketAdapter()
        return YahooAdapter()
    cfg = get_provider_config("rakuten")
    if not cfg or not cfg.get("api_key"):
        return MockMarketAdapter()
    return RakutenAdapter()


def get_search_adapter():
    if _use_mock():
        return TavilyAdapter()
    cfg = get_provider_config("tavily")
    if not cfg or not cfg.get("api_key"):
        return TavilyAdapter()  # 同一実装だが将来差替
    return TavilyAdapter()


def get_llm():
    # OpenAIは未設定でもOpenAILLM側がフェールソフト
    return OpenAILLM()