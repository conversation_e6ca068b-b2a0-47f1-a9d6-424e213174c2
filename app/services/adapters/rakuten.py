from typing import List, Dict, Any, Optional
import requests
import os
import urllib.parse
from app.services.adapters.mock_provider import MockMarketAdapter
from app.data_access.repositories import get_provider_config


class RakutenAdapter(MockMarketAdapter):
    """
    楽天ウェブサービスAPIアダプター
    API Documentation: https://webservice.rakuten.co.jp/documentation

    対応API:
    - 楽天市場商品検索API (IchibaItem/Search)
    - 楽天市場ランキングAPI (IchibaItem/Ranking)
    - 楽天市場ジャンル検索API (IchibaGenre/Search)
    - 楽天市場タグ検索API (IchibaTag/Search)
    - 楽天商品価格ナビ製品検索API (Product/Search)
    """

    def __init__(self):
        """
        認証情報を設定から取得
        """
        config = get_provider_config("rakuten")
        if config:
            self.app_id = config.get("api_key")  # 楽天アプリケーションID
            self.affiliate_id = config.get("affiliate_id")  # 楽天アフィリエイトID
        else:
            # 環境変数からフォールバック
            self.app_id = os.environ.get("RAKUTEN_APP_ID")
            self.affiliate_id = os.environ.get("RAKUTEN_AFFILIATE_ID")

        self.base_url = "https://app.rakuten.co.jp/services/api"
        self.format_version = 2  # 新しいレスポンス形式を使用

    def _make_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        楽天APIへのリクエストを実行
        """

        # 共通パラメータを追加
        request_params = {
            "applicationId": self.app_id,
            "format": "json",
            "formatVersion": self.format_version,
            **params
        }

        # アフィリエイトIDがある場合は追加
        if self.affiliate_id:
            request_params["affiliateId"] = self.affiliate_id


        print(f"Making request to Rakuten API: {endpoint}")
        print(f"Parameters: {request_params}")

        response = requests.get(
                f"{self.base_url}/{endpoint}",
                params=request_params,
                timeout=30
            )

        print(f"Response status: {response.status_code}")
        response.raise_for_status()

        result = response.json()
        print(f"Response keys: {list(result.keys()) if isinstance(result, dict) else type(result)}")
        return result


    def _parse_rakuten_items(self, api_response: Dict[str, Any], query: str = "") -> List[Dict[str, Any]]:
        """
        楽天APIのレスポンスを標準形式に変換
        """
        if "error" in api_response:
            print(f"API error: {api_response['error']}")
            return []


        results = []

        try:
            # 楽天APIの標準レスポンス構造
            items = api_response.get('Items', [])
            if not items:
                items = api_response.get('items', [])

            for i, item_wrapper in enumerate(items[:10]):  # 最大10件
                try:
                    # formatVersion=2の場合は直接アクセス、formatVersion=1の場合はitem.itemでアクセス
                    if 'item' in item_wrapper:
                        item = item_wrapper['item']
                    else:
                        item = item_wrapper

                    # 価格の抽出
                    price = 0.0
                    if 'itemPrice' in item:
                        price = float(item['itemPrice'])
                    elif 'price' in item:
                        price = float(item['price'])

                    # 画像URLの抽出
                    image_urls = []
                    if 'mediumImageUrls' in item:
                        image_urls = item['mediumImageUrls']
                    elif 'imageFlag' in item and item['imageFlag'] == 1:
                        image_urls = [item.get('mediumImageUrl', '')]

                    # 商品URLの構築
                    item_url = item.get('itemUrl', item.get('affiliateUrl', ''))

                    results.append({
                        "listing_id": item.get('itemCode', f"RAKUTEN-{i}"),
                        "sku": item.get('itemCode', f"SKU-{i}"),
                        "title": item.get('itemName', f"{query} - 楽天商品{i}"),
                        "price": price,
                        "stock": 100,  # 楽天APIでは在庫情報が直接取得できない
                        "reviews": item.get('reviewCount', 0),
                        "rating": item.get('reviewAverage', 0.0),
                        "url": item_url,
                        "shop_name": item.get('shopName', ''),
                        "shop_code": item.get('shopCode', ''),
                        "genre_id": item.get('genreId', ''),
                        "images": image_urls,
                        "description": item.get('itemCaption', ''),
                        "is_tax_included": item.get('taxFlag', 0) == 1,
                        "postage_flag": item.get('postageFlag', 0),
                        "credit_card_flag": item.get('creditCardFlag', 0) == 1,
                        "shop_of_the_year_flag": item.get('shopOfTheYearFlag', 0) == 1,
                        "affiliate_rate": item.get('affiliateRate', 0.0),
                        "start_time": item.get('startTime', ''),
                        "end_time": item.get('endTime', '')
                    })
                except Exception as e:
                    print(f"Error parsing item {i}: {e}")
                    continue

            print(f"Parsed {len(results)} items from Rakuten API response")
            return results

        except Exception as e:
            print(f"Error parsing Rakuten results: {e}")
            import traceback
            traceback.print_exc()
            return []

    def search_products(
            self,
            # --- 基本 ---
            query: Optional[str] = None,  # keyword
            page: int = 1,
            hits: int = 30,
            sort: str = "standard",
            elements: Optional[str] = None,

            # --- 価格 ---
            min_price: Optional[int] = None,
            max_price: Optional[int] = None,

            # --- 絞り込み（在庫/画像/レビュー等） ---
            availability: Optional[int] = None,  # 0:すべて / 1:在庫あり
            image_flag: Optional[int] = None,  # 0/1
            has_review_flag: Optional[int] = None,  # 0/1
            has_movie_flag: Optional[int] = None,  # 0/1
            gift_flag: Optional[int] = None,  # 0/1
            credit_card_flag: Optional[int] = None,  # 0/1
            postage_flag: Optional[int] = None,  # 0/1（送料込のみ等）
            point_rate_flag: Optional[int] = None,  # 0/1
            point_rate: Optional[int] = None,  # 1..?
            asuraku_flag: Optional[int] = None,  # 0/1
            asuraku_area: Optional[int] = None,  # 都道府県コード
            appoint_delivery_date_flag: Optional[int] = None,  # 0/1
            pamphlet_flag: Optional[int] = None,  # 0/1

            # --- キーワード/検索挙動 ---
            or_flag: Optional[int] = None,  # 0/1（OR検索）
            ng_keyword: Optional[str] = None,  # 除外キーワード
            field: Optional[int] = None,  # 0:全体 / 1:商品名のみ
            carrier: Optional[int] = None,  # 0:PC / 1:モバイル / 2:スマホ 等

            # --- ジャンル/タグ/店舗 ---
            genre_id: Optional[int] = None,
            tag_id: Optional[int] = None,
            shop_code: Optional[str] = None,

            # --- 配送/海外/購入タイプ ---
            ship_overseas_flag: Optional[int] = None,  # 0/1
            ship_overseas_area: Optional[str] = None,  # “US” 等
            purchase_type: Optional[int] = None,  # 0:通常/1:定期 等

            # --- アフィリエイト ---
            min_affiliate_rate: Optional[int] = None,
            max_affiliate_rate: Optional[int] = None,

            # --- 将来互換・未網羅分の生パラメータ透過 ---
            **extra_params: Any,
    ) -> List[Dict[str, Any]]:
        """
        楽天市場商品検索API (IchibaItem/Search v2022-06-01)
        Docs: https://webservice.rakuten.co.jp/documentation/ichiba-item-search
        """
        params: Dict[str, Any] = {
            "formatVersion": 2,  # v2 レスポンス（items[].itemName ...）
            "page": page,  # 1–100
            "hits": hits,  # 1–30
            "sort": sort  # standard / ±itemPrice / ±reviewCount / ±reviewAverage / ±updateTimestamp / ±affiliateRate
        }

        # 基本
        if query is not None:
            params["keyword"] = query
        if elements is not None:
            params["elements"] = elements

        # 価格
        if min_price is not None:
            params["minPrice"] = min_price
        if max_price is not None:
            params["maxPrice"] = max_price

        # 絞り込み
        if availability is not None:
            params["availability"] = availability
        if image_flag is not None:
            params["imageFlag"] = image_flag
        if has_review_flag is not None:
            params["hasReviewFlag"] = has_review_flag
        if has_movie_flag is not None:
            params["hasMovieFlag"] = has_movie_flag
        if gift_flag is not None:
            params["giftFlag"] = gift_flag
        if credit_card_flag is not None:
            params["creditCardFlag"] = credit_card_flag
        if postage_flag is not None:
            params["postageFlag"] = postage_flag
        if point_rate_flag is not None:
            params["pointRateFlag"] = point_rate_flag
        if point_rate is not None:
            params["pointRate"] = point_rate
        if asuraku_flag is not None:
            params["asurakuFlag"] = asuraku_flag
        if asuraku_area is not None:
            params["asurakuArea"] = asuraku_area
        if appoint_delivery_date_flag is not None:
            params["appointDeliveryDateFlag"] = appoint_delivery_date_flag
        if pamphlet_flag is not None:
            params["pamphletFlag"] = pamphlet_flag

        # 検索挙動
        if or_flag is not None:
            params["orFlag"] = or_flag
        if ng_keyword is not None:
            params["NGKeyword"] = ng_keyword
        if field is not None:
            params["field"] = field
        if carrier is not None:
            params["carrier"] = carrier

        # ジャンル/タグ/店舗
        if genre_id is not None:
            params["genreId"] = genre_id
        if tag_id is not None:
            params["tagId"] = tag_id
        if shop_code is not None:
            params["shopCode"] = shop_code

        # 配送/海外/購入タイプ
        if ship_overseas_flag is not None:
            params["shipOverseasFlag"] = ship_overseas_flag
        if ship_overseas_area is not None:
            params["shipOverseasArea"] = ship_overseas_area
        if purchase_type is not None:
            params["purchaseType"] = purchase_type

        # アフィリエイト
        if min_affiliate_rate is not None:
            params["minAffiliateRate"] = min_affiliate_rate
        if max_affiliate_rate is not None:
            params["maxAffiliateRate"] = max_affiliate_rate

        # 追加パラメータ（将来拡張/ドキュメント外でも透過）
        if extra_params:
            params.update({k: v for k, v in extra_params.items() if v is not None})

        api_response = self._make_request("IchibaItem/Search/20220601", params)
        return self._parse_rakuten_items(api_response, query or "")


    def get_ranking(self, genre_id: Optional[int] = None, age: int = 30,
                   sex: int = 0, carrier: int = 0, page: int = 1) -> List[Dict[str, Any]]:
        """
        楽天市場ランキングAPI (IchibaItem/Ranking)

        Args:
            genre_id: ジャンルID（指定しない場合は総合ランキング）
            age: 年代（0:全年代, 10:10代, 20:20代, 30:30代, 40:40代, 50:50代以上）
            sex: 性別（0:男女, 1:男性, 2:女性）
            carrier: キャリア（0:PC, 1:モバイル）
            page: ページ番号
        """
        params = {
            "age": age,
            "sex": sex,
            "carrier": carrier,
            "page": page
        }

        if genre_id is not None:
            params["genreId"] = genre_id

        api_response = self._make_request("IchibaItem/Ranking/20170628", params)
        return self._parse_rakuten_items(api_response, "ranking")

    def get_genres(self, genre_id: int = 0) -> List[Dict[str, Any]]:
        """
        楽天市場ジャンル検索API (IchibaGenre/Search)

        Args:
            genre_id: ジャンルID（0の場合は大カテゴリを取得）
        """
        params = {
            "genreId": genre_id
        }

        try:
            api_response = self._make_request("IchibaGenre/Search/20140222", params)

            if "error" in api_response:
                return []

            # ジャンル情報の解析
            genres = []
            children = api_response.get('children', [])
            current = api_response.get('current', {})

            # 現在のジャンル情報
            if current:
                genres.append({
                    "genre_id": current.get('genreId'),
                    "genre_name": current.get('genreName'),
                    "genre_level": current.get('genreLevel'),
                    "parent_genre_id": current.get('parentGenreId')
                })

            # 子ジャンル情報
            for child in children:
                genres.append({
                    "genre_id": child.get('genreId'),
                    "genre_name": child.get('genreName'),
                    "genre_level": child.get('genreLevel'),
                    "parent_genre_id": child.get('parentGenreId')
                })

            return genres

        except Exception as e:
            print(f"Error getting genres: {e}")
            return []

    def get_tags(self, tag_id: int) -> Dict[str, Any]:
        """
        楽天市場タグ検索API (IchibaTag/Search)

        Args:
            tag_id: タグID
        """
        params = {
            "tagId": tag_id
        }

        try:
            api_response = self._make_request("IchibaTag/Search/20140222", params)

            if "error" in api_response:
                return {}


            return api_response

        except Exception as e:
            print(f"Error getting tags: {e}")
            return {}
