#!/usr/bin/env python3
"""
RakutenAdapter APIテスト（モックデータなし）
実際のAPIコールのみをテスト
"""

import os
import sys
from dotenv import load_dotenv
from app.services.adapters.rakuten import RakutenAdapter

# .envファイルを読み込み
load_dotenv()


def test_rakuten_api_calls():
    """
    RakutenAdapterの実際のAPIコールをテスト
    """
    print("=" * 80)
    print("🛒 Rakuten API コールテスト開始")
    print("=" * 80)

    # 認証情報確認
    app_id = os.environ.get("RAKUTEN_APP_ID")
    affiliate_id = os.environ.get("RAKUTEN_AFFILIATE_ID")

    if not app_id:
        print("❌ Rakuten App IDが設定されていません")
        print("環境変数 RAKUTEN_APP_ID を設定してください")
        return False

    print(f"✓ App ID: {app_id}")
    if affiliate_id:
        print(f"✓ Affiliate ID: {affiliate_id[:10]}...")

    results = {}
    adapter = RakutenAdapter()

    # 1. search_products テスト
    print(f"\n🔍 1. search_products テスト")
    try:
        search_results = adapter.search_products("Nintendo Switch", hits=3)
        if search_results and len(search_results) > 0:
            print(f"✅ 検索結果: {len(search_results)}件")
            print(f"   最初の商品: {search_results[0].get('title', 'N/A')[:50]}...")
            print(f"   価格: ¥{search_results[0].get('price', 0):,}")
            print(f"   ショップ: {search_results[0].get('shop_name', 'N/A')}")
            results['search_products'] = True
        else:
            print("❌ 検索結果が取得できませんでした")
            results['search_products'] = False
    except Exception as e:
        print(f"❌ エラー: {e}")
        results['search_products'] = False

    # 2. get_ranking テスト
    print(f"\n🏆 2. get_ranking テスト")
    try:
        ranking = adapter.get_ranking()
        if ranking and len(ranking) > 0:
            print(f"✅ ランキング: {len(ranking)}件")
            print(f"   1位: {ranking[0].get('title', 'N/A')[:50]}...")
            print(f"   価格: ¥{ranking[0].get('price', 0):,}")
            print(f"   ショップ: {ranking[0].get('shop_name', 'N/A')}")
            results['get_ranking'] = True
        else:
            print("❌ ランキングが取得できませんでした")
            results['get_ranking'] = False
    except Exception as e:
        print(f"❌ エラー: {e}")
        results['get_ranking'] = False

    # 3. get_genres テスト
    print(f"\n📂 3. get_genres テスト")
    try:
        genres = adapter.get_genres(0)
        if genres and len(genres) > 0:
            print(f"✅ ジャンル情報: {len(genres)}件")
            if genres[0].get('genre_name'):
                print(f"   最初のジャンル: {genres[0].get('genre_name', 'N/A')}")
                print(f"   ジャンルID: {genres[0].get('genre_id', 'N/A')}")
            results['get_genres'] = True
        else:
            print("❌ ジャンル情報が取得できませんでした")
            results['get_genres'] = False
    except Exception as e:
        print(f"❌ エラー: {e}")
        results['get_genres'] = False

    # 4. get_tags テスト
    print(f"\n🏷️ 4. get_tags テスト")
    try:
        tags = adapter.get_tags(1001077)
        if tags and tags.get('tagGroups'):
            print(f"✅ タグ情報取得成功")
            print(f"   タググループ: {[g.get('tagGroupName') for g in tags.get('tagGroups', [])]}")
            print(f"   タグ数: {sum([len(g.get('tags', [])) for g in tags.get('tagGroups', [])])}件")
            results['get_tags'] = True
        else:
            print("❌ タグ情報が取得できませんでした")
            results['get_tags'] = False
    except Exception as e:
        print(f"❌ エラー: {e}")
        results['get_tags'] = False

    # 結果サマリー
    print(f"\n" + "=" * 80)
    print("📊 APIテスト結果サマリー")
    print("=" * 80)

    success_count = sum(results.values())
    total_count = len(results)

    for method, success in results.items():
        status = "✅ 成功" if success else "❌ 失敗"
        print(f"{method:20} : {status}")

    print(f"\n成功率: {success_count}/{total_count} ({success_count / total_count * 100:.1f}%)")

    if success_count == total_count:
        print("🎉 すべてのAPIコールが成功しました！")
    elif success_count > 0:
        print("⚠️ 一部のAPIコールが失敗しました")
    else:
        print("❌ すべてのAPIコールが失敗しました")

    print("=" * 80)
    return success_count == total_count


if __name__ == "__main__":
    test_rakuten_api_calls()
