#!/usr/bin/env python3
"""
更新されたOxyLabsAdapter APIテスト（モックデータなし）
実際のAPIコールのみをテスト
"""

import os
import sys
from dotenv import load_dotenv
from app.services.adapters.oxylabs import OxyLabsAdapter

# .envファイルを読み込み
load_dotenv()


def test_updated_oxylabs_api_calls():
    # 認証情報確認
    username = os.environ.get("OXYLABS_USER")
    password = os.environ.get("OXYLABS_PASS")

    if not username or not password:
        print("❌ OxyLabs認証情報が設定されていません")
        print("環境変数 OXYLABS_USER と OXYLABS_PASS を設定してください")
        return False

    print(f"✓ Username: {username}")
    print(f"✓ Password: {'*' * len(password)}")

    # OxyLabsAdapterのインスタンス化
    results = {}
    adapter = OxyLabsAdapter()
    test_asin = "B08PTPTMH5"  # テスト用ASIN

    # 1. search_products テスト
    print(f"\n🔍 1. search_products テスト")
    try:
        search_results = adapter.search_products("AirPods Pro", pages=1)
        if search_results and not search_results.get('error'):
            print(f"✅ 検索結果取得成功")
            print(f"   レスポンスタイプ: {type(search_results)}")
            if isinstance(search_results, dict):
                print(f"   レスポンスキー: {list(search_results.keys())}")
            results['search_products'] = True
        else:
            print("❌ 検索結果が取得できませんでした")
            results['search_products'] = False
    except Exception as e:
        print(f"❌ エラー: {e}")
        results['search_products'] = False

    # 2. get_product テスト
    print(f"\n📦 2. get_product テスト (ASIN: {test_asin})")
    try:
        product = adapter.get_product(test_asin)
        print(f"product: {product}")
        if product and not product.get('error'):
            print(f"✅ 商品詳細取得成功")
            print(f"   レスポンスタイプ: {type(product)}")
            if isinstance(product, dict):
                print(f"   レスポンスキー: {list(product.keys())}")
            results['get_product'] = True
        else:
            print("❌ 商品詳細が取得できませんでした")
            results['get_product'] = False
    except Exception as e:
        print(f"❌ エラー: {e}")
        results['get_product'] = False

    # 3. get_pricing テスト
    print(f"\n💰 3. get_pricing テスト (ASIN: {test_asin})")
    try:
        pricing = adapter.get_pricing(test_asin)
        if pricing and not pricing.get('error'):
            print(f"✅ 価格情報取得成功")
            print(f"   レスポンスタイプ: {type(pricing)}")
            if isinstance(pricing, dict):
                print(f"   レスポンスキー: {list(pricing.keys())}")
            results['get_pricing'] = True
        else:
            print("❌ 価格情報が取得できませんでした")
            results['get_pricing'] = False
    except Exception as e:
        print(f"❌ エラー: {e}")
        results['get_pricing'] = False

    # 4. get_sellers テスト
    print(f"\n🏪 4. get_sellers テスト (Seller ID: {test_asin})")
    try:
        sellers = adapter.get_sellers(test_asin)
        if sellers and not (isinstance(sellers, dict) and sellers.get('error')):
            print(f"✅ 販売者情報取得成功")
            print(f"   レスポンスタイプ: {type(sellers)}")
            if isinstance(sellers, dict):
                print(f"   レスポンスキー: {list(sellers.keys())}")
            results['get_sellers'] = True
        else:
            print("❌ 販売者情報が取得できませんでした")
            results['get_sellers'] = False
    except Exception as e:
        print(f"❌ エラー: {e}")
        results['get_sellers'] = False

    # 6. get_best_sellers テスト
    print(f"\n🏆 6. get_best_sellers テスト")
    try:
        bestsellers = adapter.get_best_sellers("electronics", pages=1)
        if bestsellers and not (isinstance(bestsellers, dict) and bestsellers.get('error')):
            print(f"✅ ベストセラー情報取得成功")
            print(f"   レスポンスタイプ: {type(bestsellers)}")
            if isinstance(bestsellers, dict):
                print(f"   レスポンスキー: {list(bestsellers.keys())}")
            results['get_best_sellers'] = True
        else:
            print("❌ ベストセラー情報が取得できませんでした")
            results['get_best_sellers'] = False
    except Exception as e:
        print(f"❌ エラー: {e}")
        results['get_best_sellers'] = False

    # 結果サマリー
    print(f"\n" + "=" * 80)
    print("📊 更新されたAPIテスト結果サマリー")
    print("=" * 80)

    success_count = sum(results.values())
    total_count = len(results)

    for method, success in results.items():
        status = "✅ 成功" if success else "❌ 失敗"
        print(f"{method:20} : {status}")

    print(f"\n成功率: {success_count}/{total_count} ({success_count / total_count * 100:.1f}%)")

    if success_count == total_count:
        print("🎉 すべてのAPIコールが成功しました！")
    elif success_count > 0:
        print("⚠️ 一部のAPIコールが失敗しました")
    else:
        print("❌ すべてのAPIコールが失敗しました")

    print("=" * 80)
    return success_count == total_count


if __name__ == "__main__":
    test_updated_oxylabs_api_calls()
