from typing import Dict, Any, List, Optional
import requests
import os
from app.services.adapters.mock_provider import MockMarketAdapter
from app.data_access.repositories import get_provider_config


class OxyLabsAdapter:
    """
    OxyLabs Web Scraper API for Amazon scraping
    API Documentation: https://developers.oxylabs.io/scraping-solutions/web-scraper-api/targets/amazon
    """

    def __init__(self):
        """
        認証情報を設定から取得
        """
        config = get_provider_config("oxylabs")
        if config:
            self.username = config.get("api_key")  # OxyLabsのユーザー名
            self.password = config.get("secret")   # OxyLabsのパスワード
        else:
            # 環境変数からフォールバック
            self.username = os.environ.get("OXYLABS_USER")
            self.password = os.environ.get("OXYLABS_PASS")

        self.base_url = "https://realtime.oxylabs.io/v1/queries"
        self.domain = "co.jp"  # Amazon Japan


    # def fetch(self, url: str) -> Dict[str, Any]:
    #     # PoC: 直接GET（実運用はOxyLabs経由に変更）
    #     try:
    #         r = requests.get(url, timeout=10)
    #         r.raise_for_status()
    #         return {"url": url, "status": r.status_code, "content": r.text[:5000]}
    #     except Exception as e:
    #         return {"url": url, "error": str(e)}

    def _make_request(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        OxyLabs APIへのリクエストを実行
        """
        if not self.username or not self.password:
            print("Error: No OxyLabs credentials provided")
            return {"error": "No credentials provided"}

        try:
            print(f"Making request to OxyLabs with payload: {payload}")
            response = requests.post(
                self.base_url,
                auth=(self.username, self.password),
                json=payload,
                timeout=60,  # Amazon scraping can take longer
                headers={'Content-Type': 'application/json'}
            )
            print(f"Response status: {response.status_code}")
            response.raise_for_status()
            result = response.json()
            print(f"Response data keys: {list(result.keys()) if isinstance(result, dict) else type(result)}")
            return result
        except requests.exceptions.RequestException as e:
            print(f"OxyLabs API request error: {e}")
            return {"error": str(e)}
        except Exception as e:
            print(f"OxyLabs API error: {e}")
            return {"error": str(e)}



    # def products_reviews(self, asin: str, pages: int = 1, geo_location: Optional[str] = None) -> List[Dict[str, Any]]:
    #     """
    #     特定のAmazon商品のレビューを取得
    #
    #     Args:
    #         asin: Amazon ASIN
    #         pages: 取得するページ数
    #     """
    #     payload = {
    #         'source': 'amazon_reviews',
    #         'domain': self.domain,
    #         'query': asin,
    #         'parse': True
    #     }
    #
    #
    #     results = self._make_request(payload)
    #
    #     return results if results else []


    def search_products(self, query: str, pages: int = 1, geo_location: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Amazon商品検索

        Args:
            query: 検索クエリ
            pages: 取得するページ数（デフォルト: 1）
        """
        payload = {
            'source': 'amazon_search',
            'domain': self.domain,
            'query': query,
            'pages': pages,
            'parse': True  # 構造化データを取得
        }
        results = self._make_request(payload)
        return results if results else []

    def get_product(self, asin: str, geo_location: Optional[str] = None) -> Dict[str, Any]:
        """
        特定のAmazon商品詳細を取得

        Args:
            asin: Amazon ASIN (例: "B07FZ8S74R")
        """

        payload = {
            'source': 'amazon_product',
            'domain': self.domain,
            'query': asin,
            'parse': True  # 構造化データを取得
        }

        results = self._make_request(payload)
        return results if results else {}

    def get_pricing(self, asin: str, geo_location: Optional[str] = None) -> Dict[str, Any]:
        """
        特定のAmazon商品の価格情報を取得

        Args:
            asin: Amazon ASIN
        """
        payload = {
            'source': 'amazon_pricing',
            'domain': self.domain,
            'query': asin,
            'parse': True
        }


        results = self._make_request(payload)
        print(f"get_pricing results: {results}")
        return results if results else {}

    def get_sellers(self, SellerID: str, geo_location: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        特定のAmazon商品の販売者情報を取得

        Args:
            Seller ID
        """
        payload = {
            'source': 'amazon_sellers',
            'domain': self.domain,
            'query': SellerID,
            'parse': True
        }

        results = self._make_request(payload)
        return results if results else []



    def get_best_sellers(self, browse_node_id: str, pages: int = 1, geo_location: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        特定のカテゴリのベストセラーを取得

        Args:
        Browse node ID
        """

        payload = {
            'source': 'amazon_bestsellers',
            'domain': self.domain,
            'query': browse_node_id,
            'pages': pages,
            'parse': True
        }


        results = self._make_request(payload)
        return results if results else []

