from typing import List, Dict, Any
import random

class MockMarketAdapter:
    def search_products(self, query: str) -> List[Dict[str, Any]]:
        return self._generate(query)

    def get_product(self, listing_id: str) -> Dict[str, Any]:
        base = self._generate("product")[0]
        base.update({"listing_id": listing_id})
        return base

    def fetch_competitors(self, key: str) -> List[Dict[str, Any]]:
        return self._generate(key)

    def _generate(self, seed: str) -> List[Dict[str, Any]]:
        random.seed(seed)
        out = []
        for i in range(10):
            price = random.randint(3000, 15000)
            stock = random.randint(0, 500)
            reviews = random.randint(0, 5000)
            out.append({
                "listing_id": f"LIST-{i}",
                "sku": f"SKU-{i}",
                "title": f"{seed} - 競合{i}",
                "price": price,
                "stock": stock,
                "reviews": reviews,
                "rating": round(random.uniform(3.0, 5.0), 1),
                "url": f"https://example.com/{seed}/{i}",
            })
        return out