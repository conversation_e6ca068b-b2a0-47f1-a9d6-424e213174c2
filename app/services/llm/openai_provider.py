import os
import json
from typing import Dict, Any, List
from app.data_access.repositories import get_provider_config
from openai import OpenAI

class OpenAILLM:
    def __init__(self):
        # 1) ENV を優先, 2) DB(providers_config.name="openai") を次点
        env_key = os.environ.get("OPENAI_API_KEY")
        db_cfg = get_provider_config("openai")
        self.api_key = env_key or (db_cfg.get("api_key") if db_cfg else None)
        # モデルは ENV > DB.extra_json.OPENAI_MODEL > デフォルト
        db_model = None
        if db_cfg and db_cfg.get("extra_json"):
            extra = db_cfg["extra_json"]
            if isinstance(extra, dict):
                db_model = extra.get("OPENAI_MODEL")
        self.model = os.environ.get("OPENAI_MODEL", db_model or "gpt-4o-mini")
        print(f"*** OpenAILLM: {self.api_key} {self.model} {OpenAI}")
        self.provider_client = OpenAI(api_key=self.api_key)

    def analyze_copy(self, text: str) -> Dict[str, Any]:
        if not self.provider_client:
            return {
                "readability": 0.78,
                "sentiment": 0.65,
                "key_points": ["快適", "高音質", "長時間"],
                "improvements": ["導入1文を短く", "具体的な数値の追加"],
            }
        try:
            prompt = (
                "以下の日本語販促文を簡潔に評価し、JSONで出力してください。\n"
                "- readability: 0-1 の可読性指標\n"
                "- sentiment: 0-1 のポジティブ度\n"
                "- key_points: 重要なキーワードを3-5件\n"
                "- improvements: 改善提案を2-4件\n"
                "JSON以外の説明文は不要です。\n\n"
                f"テキスト:\n{text}"
            )
            resp = self.provider_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "あなたは日本語のECコピーの分析アシスタントです。"},
                    {"role": "user", "content": prompt},
                ],
                temperature=0.2,
                max_tokens=300,
            )
            content = resp.choices[0].message.content if resp and resp.choices else "{}"
            data = json.loads(content)
            if isinstance(data, dict):
                return data  # type: ignore[return-value]
        except Exception:
            pass
        # 失敗時フォールバック（最小限の安全策）
        return {
            "readability": 0.8,
            "sentiment": 0.7,
            "key_points": ["快適", "高音質", "長時間"],
            "improvements": ["訴求点の優先度整理", "価格根拠の補強"],
        }

    def generate_copy(self, product: Dict[str, Any], tone: str, max_tokens: int) -> str:
        if not self.provider_client:
            return (
                f"{product.get('title','商品')}の魅力を、{tone}に。長時間でも快適、毎日を彩るサウンド体験。"
            )
        title = product.get("title", "商品")
        features = product.get("features", "")
        try:
            prompt = (
                "以下の条件で日本語のEC向け販促コピーを1案だけ生成してください。\n"
                f"- 商品名: {title}\n"
                f"- 特徴: {features}\n"
                f"- トーン: {tone}\n"
                "- 箇条書きではなく、短い文章1-2文で。\n"
                "- 絵文字は使わない。\n"
                "- 出力はコピー本文のみ。余計な説明は不要。\n"
            )
            resp = self.provider_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "あなたは日本語のECコピーライターです。"},
                    {"role": "user", "content": prompt},
                ],
                temperature=0.7,
                max_tokens=max(64, min(512, int(max_tokens) if isinstance(max_tokens, int) else 200)),
            )
            return (resp.choices[0].message.content or "").strip()
        except Exception:
            return f"{title}の価値を、{tone}に。ノイズを抑え、集中を高めるデザイン。"

    def suggest_tags(self, product: Dict[str, Any], marketplace: str) -> List[str]:
        print(f"*** start suggest_tags: {product} {marketplace}")
        title = product.get("title", "商品")
        competitor_titles: List[str] = product.get("competitor_titles", []) if isinstance(product, dict) else []
        web_snippets: List[str] = product.get("web_snippets", []) if isinstance(product, dict) else []

        print(f"**** self client: {self.provider_client}")
        if not self.provider_client:
            # 明示的に LLM 依存とするため、フォールバックは空配列を返す
            return []
        try:
            # プロンプトを安全に組み立て
            prompt_lines: List[str] = [
                "以下の条件で日本語の検索・出品向けタグ候補を生成してください。",
                f"- 商品名: {title}",
                f"- マーケット: {marketplace}",
            ]
            if competitor_titles:
                prompt_lines.append("- 競合商品タイトル例: " + ", ".join(competitor_titles[:10]))
            if web_snippets:
                snippet_joined = " ".join([str(s)[:200] for s in web_snippets[:3]])
                prompt_lines.append("- 関連ページ抜粋(要約可): " + snippet_joined)
            prompt_lines.extend([
                "- 目的: 検索流入と購入転換の両方を意識した実用的なタグ。",
                "- 指示: 単なる商品名の繰り返しを避け、用途/ベネフィット/互換性/仕様/規格/対象ユーザー/シーン/ロングテールも網羅。",
                "- 指示: 同義語・略称・英字表記（例: USB-C/Type-C）も適宜含める。",
                "- 指示: 日本語の一般名詞・名詞句で、# は付けない。1タグは20文字以内。",
                "- 出力: JSONオブジェクトのみ（例: {\"tags\": [\"タグ1\", \"タグ2\"]}）。説明文は不要。",
                "- 件数: 12〜20件。重複・冗長は避ける。",
            ])
            prompt = "\n".join(prompt_lines)
            resp = self.provider_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "あなたは日本語のECタグ生成アシスタントです。"},
                    {"role": "user", "content": prompt},
                ],
                temperature=0.4,
                max_tokens=400,
                response_format={"type": "json_object"},
            )
            content = resp.choices[0].message.content if resp and resp.choices else "{}"
            try:
                data = json.loads(content or "{}")
                if isinstance(data, dict) and isinstance(data.get("tags"), list):
                    tags = [t for t in data.get("tags", []) if isinstance(t, str)]
                    if tags:
                        return tags[:20]
            except Exception as e:
                print(f"Error in suggest_tags: {e}")
                # カンマ区切り等の簡易パース
                if content:
                    parts = [p.strip().strip('"\'') for p in content.split(",")]
                    parts = [p for p in parts if p]
                    if parts:
                        return parts[:20]
        except Exception as e:
            print(f"Error in suggest_tags: {e}")
            pass
        # フォールバック: LLM 必須方針のため空配列
        return []