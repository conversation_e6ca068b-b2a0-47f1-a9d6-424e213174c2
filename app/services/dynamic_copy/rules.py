from typing import Dict, Any
from app.services.llm.openai_provider import OpenAILLM

templates = {
    "default": "毎日に寄り添う高音質。今すぐ体験。",
    "low_stock": "在庫わずか。お早めに。",
    "sale": "期間限定セール中。今だけ特別価格。",
    "winter": "冬の室内でも快適な音。温かい時間を。",
    "summer": "夏の外出にも軽やかなサウンド。",
}

def select_copy_variant(conditions: Dict[str, Any], product: Dict[str, Any] | None = None) -> str:
    text = templates["default"]
    if conditions.get("low_stock"):
        text = templates["low_stock"]
    if conditions.get("sale"):
        text = text + " " + templates["sale"]
    season = conditions.get("season")
    if season in templates:
        text = text + " " + templates[season]

    # LLMで整える（API未設定ならそのまま）
    llm = OpenAILLM()
    improved = llm.generate_copy(product or {"title": "商品"}, tone="カジュアル", max_tokens=300)
    return f"{text} {improved}"