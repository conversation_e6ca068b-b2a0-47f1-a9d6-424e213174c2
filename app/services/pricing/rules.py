from typing import List, Dict, Any


def apply_pricing(product: Dict[str, Any], competitors: List[Dict[str, Any]], stock: int, season: str, params: Dict[str, Any]) -> Dict[str, Any]:
    base_price = float(product.get("base_price", 1000))
    cost = float(product.get("cost", base_price * 0.5))
    min_margin_pct = float(params.get("min_margin_pct", 0.1))
    follow_comp = bool(params.get("follow_competitor", True))

    competitor_prices = [c.get("price", base_price) for c in competitors if c.get("price")]
    avg_comp = sum(competitor_prices) / len(competitor_prices) if competitor_prices else base_price

    seasonal_adj = {"spring": 1.0, "summer": 1.05, "autumn": 0.98, "winter": 1.02}.get(season, 1.0)

    target = base_price * seasonal_adj
    if follow_comp:
        target = (target + avg_comp) / 2

    # 在庫が少ない場合は若干上げる、多い場合は下げる
    if stock < 20:
        target *= 1.05
    elif stock > 500:
        target *= 0.97

    # 最低利益率
    min_price = cost * (1 + min_margin_pct)
    recommended = max(target, min_price)
    recommended = round(recommended)

    rationale = [
        f"競合平均価格: {round(avg_comp)}",
        f"季節係数: {seasonal_adj}",
        f"在庫補正: {'上げ' if stock < 20 else ('下げ' if stock > 500 else '無')}",
        f"最低利益率適用: {min_margin_pct*100:.0f}%",
    ]

    simulation = {
        "before": base_price,
        "after": recommended,
        "cost": cost,
        "gross_margin_pct": round((recommended - cost) / recommended * 100, 1)
    }

    return {
        "recommended_price": recommended,
        "rationale": rationale,
        "simulation": simulation,
    }