from typing import Dict, List
from app.services.llm.openai_provider import OpenAILLM
from app.services.registry import get_market_adapter
from app.services.adapters.oxylabs import OxyLabsAdapter

constraints = {
    "yahoo": {"max_tags": 10, "max_len": 20},
    "amazon": {"max_tags": 15, "max_len": 25},
    "rakuten": {"max_tags": 12, "max_len": 20},
}

def generate_tags(product: Dict, marketplace: str) -> List[str]:
    """商品名と外部アダプター由来の文脈をもとにタグを生成する。

    - 固定ベースタグは使用しない
    - マーケットアダプターで競合情報を取得
    - OxyLabsアダプターで上位の競合ページ本文を一部取得
    - 取得した文脈を LLM に渡してタグ候補を生成
    """

    print(f"generate_tags: {product} {marketplace}")

    title = (product or {}).get("title", "").strip()

    # 競合・周辺文脈を収集
    competitor_titles: List[str] = []
    web_snippets: List[str] = []

    try:
        adapter = get_market_adapter(marketplace)  # marketplace は "amazon" | "yahoo" | "rakuten" を想定
        competitors = adapter.search_products(title) if title else []
        if competitors:
            competitor_titles = [c.get("title", "") for c in competitors if c.get("title")] [:10]
            urls = [c.get("url") for c in competitors if c.get("url")] [:3]
            oxy = OxyLabsAdapter()
            for url in urls:
                try:
                    res = oxy.fetch(url)
                    content = res.get("content")
                    if content:
                        web_snippets.append(content[:500])
                except Exception as e:
                    print(f"Error in (in competitors) generate_tags: {e}")
                    # 文脈取得はベストエフォート
                    continue
    except Exception as e:
        print(f"Error in (adapter) generate_tags: {e}")
        # 競合取得はベストエフォート
        pass

    # LLM に渡す商品情報を拡張
    llm_input: Dict = dict(product or {})
    if competitor_titles:
        llm_input["competitor_titles"] = competitor_titles
    if web_snippets:
        llm_input["web_snippets"] = web_snippets

    llm = OpenAILLM()
    print(f"llm_input: {llm_input}")
    llm_tags = llm.suggest_tags(llm_input, marketplace)
    print(f"llm_tags: {llm_tags}")

    # マーケットごとの制約を適用
    c = constraints.get(marketplace, {"max_tags": 10, "max_len": 20})
    unique_tags = list(dict.fromkeys(llm_tags))
    tags = [t[: c["max_len"]] for t in unique_tags][: c["max_tags"]]
    return tags