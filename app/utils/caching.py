import os
import time
from typing import Any, Dict

try:
    import redis  # type: ignore
except Exception:  # pragma: no cover
    redis = None


class SimpleCache:
    def __init__(self):
        self._mem: Dict[str, Any] = {}
        self.client = None
        url = os.environ.get("REDIS_URL")
        if url and redis:
            try:
                self.client = redis.Redis.from_url(url)
            except Exception:
                self.client = None

    def get(self, key: str) -> Any:
        if self.client:
            v = self.client.get(key)
            return v
        return self._mem.get(key)

    def set(self, key: str, value: Any, ex: int | None = None) -> None:
        if self.client:
            self.client.set(key, value, ex=ex)
        else:
            self._mem[key] = value


cache = SimpleCache()