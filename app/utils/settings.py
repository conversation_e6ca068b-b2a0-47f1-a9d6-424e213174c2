import os
from typing import Dict, Any

try:
    import streamlit as st  # type: ignore
except Exception:  # pragma: no cover
    st = None


def load_settings() -> Dict[str, Any]:
    data: Dict[str, Any] = {}

    # 1) Streamlit secrets（ファイル未配置時はスキップ）
    if st and hasattr(st, "secrets"):
        try:
            for k, v in st.secrets.items():
                data[k] = v
        except FileNotFoundError:
            # secrets.toml が無い場合は無視してENVにフォールバック
            pass
        except Exception:
            # 予期せぬ異常でもアプリは継続（ENV/DBを利用）
            pass

    # 2) Env
    keys = [
        "OPENAI_API_KEY", "OPENAI_MODEL",
        "TAVILY_API_KEY",
        "OXYLABS_USER", "OXYLABS_PASS",
        "YAHOO_API_KEY", "AMAZON_ACCESS_KEY_ID", "AMAZON_SECRET_ACCESS_KEY", "RAKUTEN_APP_ID",
        "DATABASE_URL", "REDIS_URL", "MOCK_MODE"
    ]
    for k in keys:
        if os.getenv(k) is not None:
            data[k] = os.getenv(k)

    # 3) DB 読み込みは PoC では省略（Admin で保存/表示のみ）

    if "MOCK_MODE" not in data:
        data["MOCK_MODE"] = True
    else:
        data["MOCK_MODE"] = str(data["MOCK_MODE"]).lower() == "true"

    return data