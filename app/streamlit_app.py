import os
import streamlit as st
from datetime import datetime

from app.utils.settings import load_settings
from app.data_access.db import run_migrations

st.set_page_config(page_title="EC SaaS PoC", page_icon="🛒", layout="wide")

# 初期化: スキーマ適用（冪等）
run_migrations()
settings = load_settings()

with st.sidebar:
    st.title("EC SaaS PoC")
    st.write("マルチページは左のページ切替からアクセスできます。")
    st.caption(f"DB: {os.environ.get('DATABASE_URL', 'not set')}")
    st.toggle("モック強制 (MOCK_MODE)", value=settings.get("MOCK_MODE", True), disabled=True)

st.header("ホーム")
st.write("各ページでモックまたは実APIを使用したPoCを体験できます。")

col1, col2 = st.columns(2)
with col1:
    st.subheader("API キー設定の状況")
    st.table({
        "OpenAI": [bool(settings.get("OPENAI_API_KEY"))],
        "Tavily": [bool(settings.get("TAVILY_API_KEY"))],
        "OxyLabs": [bool(settings.get("OXYLABS_USER") and settings.get("OXYLABS_PASS"))],
        "Yahoo": [bool(settings.get("YAHOO_API_KEY"))],
        "Amazon": [bool(settings.get("AMAZON_ACCESS_KEY_ID") and settings.get("AMAZON_SECRET_ACCESS_KEY"))],
        "Rakuten": [bool(settings.get("RAKUTEN_APP_ID"))],
    })
with col2:
    st.subheader("情報")
    st.markdown("- Admin Settings ページからAPIキーを保存できます")
    st.markdown("- 未設定時は自動でモック出力にフォールバックします")
    st.caption(datetime.utcnow().isoformat())