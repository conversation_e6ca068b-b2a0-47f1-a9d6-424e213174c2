CREATE TABLE IF NOT EXISTS providers_config (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  type TEXT NOT NULL,
  api_key TEXT,
  secret TEXT,
  extra_json JSONB,
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE UNIQUE INDEX IF NOT EXISTS providers_config_name_key ON providers_config(name);

CREATE TABLE IF NOT EXISTS products (
  id SERIAL PRIMARY KEY,
  sku TEXT UNIQUE,
  title TEXT,
  base_price NUMERIC,
  cost NUMERIC,
  stock INT,
  metadata JSONB
);

CREATE TABLE IF NOT EXISTS listings (
  id SERIAL PRIMARY KEY,
  product_id INT REFERENCES products(id),
  marketplace TEXT,
  listing_id TEXT,
  price NUMERIC,
  stock INT,
  title TEXT,
  description TEXT,
  tags_json JSONB,
  last_synced_at TIMESTAMP
);

CREATE TABLE IF NOT EXISTS competitor_listings (
  id SERIAL PRIMARY KEY,
  sku TEXT,
  marketplace TEXT,
  listing_id TEXT,
  price NUMERIC,
  stock INT,
  reviews INT,
  rating NUMERIC,
  title TEXT,
  url TEXT,
  snapshot_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS price_rules (
  id SERIAL PRIMARY KEY,
  formula TEXT,
  min_margin_pct NUMERIC,
  threshold_json JSONB
);

CREATE TABLE IF NOT EXISTS copy_variants (
  id SERIAL PRIMARY KEY,
  product_id INT,
  product_title TEXT,
  variant_type TEXT,
  text TEXT,
  source TEXT,
  metrics JSONB,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS fetch_logs (
  id SERIAL PRIMARY KEY,
  provider TEXT,
  endpoint TEXT,
  status TEXT,
  cost_ms INT,
  error TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS tasks (
  id SERIAL PRIMARY KEY,
  type TEXT,
  params_json JSONB,
  status TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);