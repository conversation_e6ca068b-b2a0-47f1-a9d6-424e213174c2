import os
import time
from sqlalchemy import create_engine, text

_engine = None


def get_engine():
    global _engine
    if _engine is None:
        url = os.environ.get("DATABASE_URL", "postgresql+psycopg2://ecuser:ecpass@localhost:5432/ecsaas")
        _engine = create_engine(url, pool_pre_ping=True, future=True)
    return _engine


def run_migrations(retries: int = 10, delay_seconds: float = 2.0):
    schema_path = os.path.join(os.path.dirname(__file__), "schema.sql")
    with open(schema_path, "r", encoding="utf-8") as f:
        sql = f.read()

    last_err = None
    for _ in range(retries):
        try:
            engine = get_engine()
            with engine.begin() as conn:
                # セミコロンで素朴に分割（空行は無視）。改行差異にも対応。
                statements = [s.strip() for s in sql.replace("\r\n", "\n").split(";")]
                for stmt in statements:
                    if stmt:
                        conn.execute(text(stmt))
            return
        except Exception as e:  # pragma: no cover
            last_err = e
            time.sleep(delay_seconds)
    if last_err:
        raise last_err