from typing import Dict, List, Any, Optional
from sqlalchemy import text
from .db import get_engine
import json


def upsert_provider_config(row: Dict[str, Any]) -> None:
    engine = get_engine()
    extra_json_val = row.get("extra_json")
    if isinstance(extra_json_val, (dict, list)):
        extra_json_val = json.dumps(extra_json_val)
    with engine.begin() as conn:
        conn.execute(text(
            """
            INSERT INTO providers_config (name, type, api_key, secret, extra_json)
            VALUES (:name, :type, :api_key, :secret, CAST(:extra_json AS JSONB))
            ON CONFLICT (name) DO UPDATE SET
                type = EXCLUDED.type,
                api_key = EXCLUDED.api_key,
                secret = EXCLUDED.secret,
                extra_json = EXCLUDED.extra_json,
                updated_at = NOW()
            """
        ), {
            "name": row.get("name"),
            "type": row.get("type"),
            "api_key": row.get("api_key"),
            "secret": row.get("secret"),
            "extra_json": extra_json_val or None,
        })


def get_all_provider_configs() -> List[Dict[str, Any]]:
    engine = get_engine()
    with engine.begin() as conn:
        res = conn.execute(text("SELECT name, type, api_key, secret, extra_json, updated_at FROM providers_config ORDER BY updated_at DESC"))
        cols = res.keys()
        return [dict(zip(cols, r)) for r in res.fetchall()]


def get_provider_config(name: str) -> Optional[Dict[str, Any]]:
    engine = get_engine()
    with engine.begin() as conn:
        res = conn.execute(text("SELECT name, type, api_key, secret, extra_json FROM providers_config WHERE name=:name"), {"name": name})
        row = res.fetchone()
        if not row:
            return None
        cols = res.keys()
        return dict(zip(cols, row))


def save_competitor_listings(rows: List[Dict[str, Any]]):
    engine = get_engine()
    with engine.begin() as conn:
        for r in rows:
            conn.execute(text(
                """
                INSERT INTO competitor_listings (sku, marketplace, listing_id, price, stock, reviews, rating, title, url)
                VALUES (:sku, :marketplace, :listing_id, :price, :stock, :reviews, :rating, :title, :url)
                """
            ), {
                "sku": r.get("sku"),
                "marketplace": r.get("marketplace", "mock"),
                "listing_id": r.get("listing_id"),
                "price": r.get("price"),
                "stock": r.get("stock"),
                "reviews": r.get("reviews"),
                "rating": r.get("rating"),
                "title": r.get("title"),
                "url": r.get("url"),
            })


def save_copy_variant(row: Dict[str, Any]):
    engine = get_engine()
    metrics_val = row.get("metrics")
    if isinstance(metrics_val, (dict, list)):
        metrics_val = json.dumps(metrics_val)
    with engine.begin() as conn:
        conn.execute(text(
            """
            INSERT INTO copy_variants (product_id, product_title, variant_type, text, source, metrics)
            VALUES (:product_id, :product_title, :variant_type, :text, :source, CAST(:metrics AS JSONB))
            """
        ), {
            "product_id": row.get("product_id"),
            "product_title": row.get("product_title"),
            "variant_type": row.get("variant_type", "ai"),
            "text": row.get("text"),
            "source": row.get("source", "llm"),
            "metrics": metrics_val or None,
        })


def list_copy_variants(limit: int = 10) -> List[Dict[str, Any]]:
    engine = get_engine()
    with engine.begin() as conn:
        res = conn.execute(text("SELECT id, product_title, variant_type, text, source, created_at FROM copy_variants ORDER BY id DESC LIMIT :limit"), {"limit": limit})
        cols = res.keys()
        return [dict(zip(cols, r)) for r in res.fetchall()]