version: "3.9"

services:
  db:
    image: postgres:16-alpine
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-ecuser}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-ecpass}
      POSTGRES_DB: ${POSTGRES_DB:-ecsaas}
    volumes:
      - db-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  app:
    build: .
    depends_on:
      - db
    environment:
      DATABASE_URL: ${DATABASE_URL:-postgresql+psycopg2://ecuser:ecpass@db:5432/ecsaas}
      REDIS_URL: ${REDIS_URL:-redis://redis:6379/0}
      OPENAI_API_KEY: ${OPENAI_API_KEY:-}
      TAVILY_API_KEY: ${TAVILY_API_KEY:-}
      OXYLABS_USER: ${OXYLABS_USER:-}
      OXYLABS_PASS: ${OXYLABS_PASS:-}
      MOCK_MODE: ${MOCK_MODE:-true}
      PYTHONPATH: /app
    command: ["sh", "-lc", "python -c 'from app.data_access.db import run_migrations; run_migrations()' && exec streamlit run app/streamlit_app.py --server.port=8501 --server.address=0.0.0.0"]
    ports:
      - "8501:8501"
    volumes:
      - ./:/app

volumes:
  db-data: