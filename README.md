# EC SaaS PoC (Streamlit + Docker)

本リポジトリは、EC（Yahoo/Amazon/楽天）向けの技術検証用PoCです。最小で動作し、各機能は未設定時でもモックに自動フォールバックします。

## 起動手順

### 1) ローカル（Python）

```bash
# ENVは適宜修正
cp .env.example .env
docker-compose up --build
```

http://localhost:8501 にアクセス。

## 環境変数

`.env.example` を参照。主なもの：
- `DATABASE_URL`: Postgres 接続
- `OPENAI_API_KEY`: OpenAI キー（未設定可）
- `TAVILY_API_KEY`, `OXYLABS_USER`, `OXYLABS_PASS`
- `YAHOO_API_KEY`, `AMAZON_ACCESS_KEY_ID`, `AMAZON_SECRET_ACCESS_KEY`, `RAKUTEN_APP_ID`
- `MOCK_MODE`: 強制モード（trueで常にモック使用）

## Vercel への Docker デプロイ

- ルートに `Dockerfile` があり、`vercel.json` で Docker を指定済み
- Vercel プロジェクトを作成し、環境変数を設定
- デプロイ：

```bash
vercel --prod
```

（またはダッシュボードからリポジトリを接続）

## 機能

- 競合・市場分析（Tavily/OxyLabs/各公式API or モック）
- AI 売れるコピー分析（LLM or モック）
- AI コピーライティング（LLM or モック、保存可）
- ダイナミックプライシング（簡易ルール）
- ダイナミックコピー（条件テンプレ + LLM補助）
- タグ生成（マーケット別制約）
- Admin 設定（APIキー保存・疎通テスト）

## 制約と今後の拡張

- 本番導入時はレート制限・APIコスト・監査ログ・秘密情報管理（KMS/HashiCorp）・スケジューラ（Celery/Cloud Scheduler）等を追加
- 例外は UI トースト表示 + `fetch_logs` に保存
- adapters の戻り値を標準化して仕様差吸収

## テスト

```bash
pytest -q
```